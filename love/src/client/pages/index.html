<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="记录我们美好爱情故事的专属网站，包含恋爱天数计算器和生日倒计时">
    <meta name="keywords" content="爱情, 情侣, 纪念日, 生日, 恋爱天数">
    <title>Yu 💕 Wang</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💕</text></svg>">
    <link rel="stylesheet" href="/src/client/styles/style.css?v=green-buttons-020">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- 自定义样式：让留言时间字体与用户名保持一致 + 留言板标题闪光效果 + 折叠按钮透明背景 -->
    <style>
        .message-date {
            font-family: 'Dancing Script', cursive !important;
            font-weight: 700 !important;
        }
        
        /* 留言板标题闪闪发光效果 */
        .messages-header h3 {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
        }
        
        @keyframes sparkle {
            0%, 100% {
                background-position: 0% 50%;
                filter: brightness(1);
            }
            25% {
                background-position: 25% 50%;
                filter: brightness(1.2);
            }
            50% {
                background-position: 100% 50%;
                filter: brightness(1.5);
            }
            75% {
                background-position: 75% 50%;
                filter: brightness(1.2);
            }
        }
        
        /* 折叠按钮透明背景 */
        .collapse-btn {
            background: transparent !important;
            border: 2px solid rgba(103, 232, 249, 0.6) !important;
        }
        
        .collapse-btn:hover {
            background: rgba(103, 232, 249, 0.1) !important;
            border-color: #67e8f9 !important;
            color: #67e8f9 !important;
        }
        
        /* 增大留言内容的字体 */
        .message-content {
            font-size: 1.4rem !important;
        }
        
        /* 增大输入框的字体和占位符文字，确保输入文字颜色与留言栏一致 */
        #messageText {
            font-size: 1.4rem !important;
            color: #d4af37 !important; /* 淡金色，与留言栏文字颜色一致 */
        }
        
        /* 确保输入框中输入的文字颜色与留言显示颜色完全一致 */
        #messageText,
        #messageText:focus,
        #messageText:active,
        textarea#messageText {
            color: #d4af37 !important; /* 淡金色，强制覆盖所有其他颜色设置 */
            caret-color: #d4af37 !important; /* 光标颜色也设为淡金色 */
        }
        
        #messageText::placeholder {
            font-size: 1.4rem !important;
        }
        
        /* 修复倾斜效果：默认不倾斜，悬停时才倾斜 */
        .counter-card h2 {
            transform: rotate(0deg) !important;
        }
        
        .counter-card h2:hover {
            transform: rotate(-1deg) scale(1.02) !important;
        }
        
        .quote-text:not(.romantic-quote-content .quote-text) {
            transform: rotate(0deg) !important;
        }
        
        .quote-text:not(.romantic-quote-content .quote-text):hover {
            transform: rotate(-1deg) scale(1.02) !important;
        }
        
        /* "写下爱的话语"闪闪发光效果 */
        .form-header h3 {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
        }
        
        /* 底部四个回忆板块不同颜色闪闪发光效果 */
        
        /* 在一起的日子 - 蓝紫色 */
        .memory-item:nth-child(1) .memory-placeholder p {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6, #8b5cf6) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3) !important;
        }
        
        .memory-item:nth-child(1) .memory-placeholder i {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6, #8b5cf6) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5), 0 0 20px rgba(139, 92, 246, 0.3) !important;
        }
        
        /* 纪念日 - 金橙色 */
        .memory-item:nth-child(2) .memory-placeholder p {
            background: linear-gradient(45deg, #f59e0b, #f97316, #f59e0b, #f97316) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(245, 158, 11, 0.5), 0 0 20px rgba(249, 115, 22, 0.3) !important;
        }
        
        .memory-item:nth-child(2) .memory-placeholder i {
            background: linear-gradient(45deg, #f59e0b, #f97316, #f59e0b, #f97316) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(245, 158, 11, 0.5), 0 0 20px rgba(249, 115, 22, 0.3) !important;
        }
        
        /* 纪念物 - 绿青色 */
        .memory-item:nth-child(3) .memory-placeholder p {
            background: linear-gradient(45deg, #10b981, #06b6d4, #10b981, #06b6d4) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(16, 185, 129, 0.5), 0 0 20px rgba(6, 182, 212, 0.3) !important;
        }
        
        .memory-item:nth-child(3) .memory-placeholder i {
            background: linear-gradient(45deg, #10b981, #06b6d4, #10b981, #06b6d4) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(16, 185, 129, 0.5), 0 0 20px rgba(6, 182, 212, 0.3) !important;
        }
        
        /* 每一次相遇 - 红粉色 */
        .memory-item:nth-child(4) .memory-placeholder p {
            background: linear-gradient(45deg, #ef4444, #ec4899, #ef4444, #ec4899) !important;
            background-size: 400% 400% !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
            animation: sparkle 2s ease-in-out infinite !important;
            text-shadow: 0 0 10px rgba(239, 68, 68, 0.5), 0 0 20px rgba(236, 72, 153, 0.3) !important;
        }
        
                 .memory-item:nth-child(4) .memory-placeholder i {
             background: linear-gradient(45deg, #ef4444, #ec4899, #ef4444, #ec4899) !important;
             background-size: 400% 400% !important;
             -webkit-background-clip: text !important;
             -webkit-text-fill-color: transparent !important;
             background-clip: text !important;
             animation: sparkle 2s ease-in-out infinite !important;
             text-shadow: 0 0 10px rgba(239, 68, 68, 0.5), 0 0 20px rgba(236, 72, 153, 0.3) !important;
         }
         
         /* 底部版权文字闪闪发光效果 - 浪漫红粉色 */
         .footer p {
             background: linear-gradient(45deg, #e91e63, #ff6b9d, #e91e63, #ff6b9d) !important;
             background-size: 400% 400% !important;
             -webkit-background-clip: text !important;
             -webkit-text-fill-color: transparent !important;
             background-clip: text !important;
             animation: sparkle 2s ease-in-out infinite !important;
             text-shadow: 0 0 10px rgba(233, 30, 99, 0.5), 0 0 20px rgba(255, 107, 157, 0.3) !important;
         }
         
                   .footer p i {
              background: linear-gradient(45deg, #e91e63, #ff6b9d, #e91e63, #ff6b9d) !important;
              background-size: 400% 400% !important;
              -webkit-background-clip: text !important;
              -webkit-text-fill-color: transparent !important;
              background-clip: text !important;
              animation: sparkle 2s ease-in-out infinite !important;
              text-shadow: 0 0 10px rgba(233, 30, 99, 0.5), 0 0 20px rgba(255, 107, 157, 0.3) !important;
          }
          
                     /* 美化情话板块 - 透明背景 */
           .quote-card {
               position: relative !important;
               background: transparent !important;
               border: 2px solid rgba(255, 255, 255, 0.3) !important;
               border-radius: 25px !important;
               padding: 35px !important;
               box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 
                          0 0 25px rgba(255, 107, 157, 0.15) !important;
               overflow: hidden !important;
           }
          
          .quote-card::before {
              content: '' !important;
              position: absolute !important;
              top: -50% !important;
              left: -50% !important;
              width: 200% !important;
              height: 200% !important;
              background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%) !important;
              animation: shimmer 4s linear infinite !important;
              pointer-events: none !important;
          }
          
                     /* 右上角换一句按钮 - 透明背景 */
           .quote-btn-top-right {
               position: absolute !important;
               top: 15px !important;
               right: 15px !important;
               background: transparent !important;
               border: 2px solid rgba(103, 232, 249, 0.6) !important;
               border-radius: 20px !important;
               padding: 8px 16px !important;
               font-size: 0.9rem !important;
               color: #67e8f9 !important;
               transition: all 0.3s ease !important;
               z-index: 10 !important;
           }
           
           .quote-btn-top-right:hover {
               background: rgba(103, 232, 249, 0.1) !important;
               border-color: #22d3ee !important;
               color: #22d3ee !important;
               transform: translateY(-2px) scale(1.05) !important;
               box-shadow: 0 8px 25px rgba(103, 232, 249, 0.3) !important;
           }
          
          /* 美化引用图标 */
          .quote-icon {
              color: #67e8f9 !important;
              font-size: 1.8rem !important;
              margin-bottom: 20px !important;
              display: block !important;
              text-shadow: 0 0 15px rgba(103, 232, 249, 0.5) !important;
          }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">🌸 加载花朵背景中...</div>
        <div class="loading-subtitle">正在为您准备美好的爱情主页</div>
        <div class="loading-progress">
            <div class="loading-progress-bar" id="loadingProgress"></div>
        </div>
    </div>

    <!-- 花朵视频背景 -->
    <div class="video-background">
        <video autoplay muted loop playsinline preload="metadata">
            <source src="/src/client/assets/videos/home/<USER>" type="video/mp4">
            <!-- 如果浏览器不支持视频，显示备用背景 -->
            您的浏览器不支持视频播放。
        </video>
    </div>

    <div class="container">
        <!-- 可点击的浪漫星星 -->
        <div class="romantic-stars">
            <div class="romantic-star star-variant-1" data-quote-category="confession" style="top: 15%; left: 20%;"></div>
            <div class="romantic-star star-variant-2" data-quote-category="sweet" style="top: 25%; left: 80%;"></div>
            <div class="romantic-star star-variant-3" data-quote-category="promise" style="top: 35%; left: 15%;"></div>
            <div class="romantic-star star-variant-4" data-quote-category="missing" style="top: 45%; left: 85%;"></div>
            <div class="romantic-star star-variant-5" data-quote-category="confession" style="top: 55%; left: 25%;"></div>
            <div class="romantic-star star-variant-6" data-quote-category="sweet" style="top: 65%; left: 75%;"></div>
            <div class="romantic-star star-variant-7" data-quote-category="promise" style="top: 75%; left: 10%;"></div>
            <div class="romantic-star star-variant-8" data-quote-category="missing" style="top: 85%; left: 90%;"></div>
            <!-- 新增更多星星，使用新的颜色变体 -->
            <div class="romantic-star star-variant-9" data-quote-category="confession" style="top: 12%; left: 60%;"></div>
            <div class="romantic-star star-variant-10" data-quote-category="sweet" style="top: 28%; left: 45%;"></div>
            <div class="romantic-star star-variant-11" data-quote-category="promise" style="top: 42%; left: 30%;"></div>
            <div class="romantic-star star-variant-12" data-quote-category="missing" style="top: 58%; left: 70%;"></div>
            <div class="romantic-star star-variant-13" data-quote-category="confession" style="top: 72%; left: 55%;"></div>
            <div class="romantic-star star-variant-14" data-quote-category="sweet" style="top: 88%; left: 35%;"></div>
            <div class="romantic-star star-variant-15" data-quote-category="promise" style="top: 18%; left: 95%;"></div>
            <div class="romantic-star star-variant-16" data-quote-category="missing" style="top: 38%; left: 5%;"></div>
        </div>

        <!-- 浪漫话语模态框 -->
        <div id="romanticModal" class="romantic-modal">
            <div class="romantic-modal-content">
                <div class="romantic-modal-header">
                    <h3>💫 星星的话语</h3>
                    <button class="close-romantic-btn" onclick="closeRomanticModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="romantic-quote-content">
                    <p class="quote-text" id="romanticQuoteText"></p>
                    <div class="quote-attribution">
                        <span class="quote-author" id="romanticQuoteAuthor"></span>
                        <span class="quote-work" id="romanticQuoteWork"></span>
                    </div>
                </div>
                <div class="romantic-modal-footer">
                    <button class="new-quote-btn" onclick="getNewQuote()">
                        <i class="fas fa-sync-alt"></i>
                        换一句
                    </button>
                </div>
            </div>
        </div>

        <!-- 流星效果 -->
        <div class="shooting-stars">
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
            <div class="shooting-star"></div>
        </div>

        <!-- 浪漫漂浮元素 -->
        <div class="hearts-container">
            <div class="heart type-1 small"></div>
            <div class="heart type-2 medium"></div>
            <div class="heart type-3 large"></div>
            <div class="heart type-4 small"></div>
            <div class="heart type-5 medium"></div>
            <div class="heart type-6 large"></div>
            <div class="heart type-7 small"></div>
            <div class="heart type-8 medium"></div>
            <div class="heart type-9 large"></div>
            <div class="heart type-10 small"></div>
            <div class="heart type-11 medium"></div>
            <div class="heart type-12 large"></div>
            <div class="heart type-1 medium"></div>
            <div class="heart type-3 small"></div>
            <div class="heart type-5 large"></div>
        </div>

        <!-- Main content -->
        <header class="header">
            <h1 class="title">
                Yu
                <i class="fas fa-heart"></i>
                Wang
            </h1>
            <p class="subtitle">Forever and Always 💕</p>
        </header>

        <main class="main-content">
            <!-- Love counter section -->
            <section class="love-counter">
                <div class="counter-card">
                    <h2>我们在一起已经</h2>
                    <div class="counter-display">
                        <div class="counter-item">
                            <span class="counter-number" id="days">0</span>
                            <span class="counter-label">天</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="hours">0</span>
                            <span class="counter-label">小时</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="minutes">0</span>
                            <span class="counter-label">分钟</span>
                        </div>
                        <div class="counter-item">
                            <span class="counter-number" id="seconds">0</span>
                            <span class="counter-label">秒</span>
                        </div>
                    </div>
                    <p class="start-date">从 2023年4月23日 开始 💖</p>
                </div>
            </section>

            <!-- Birthday section -->
            <section class="birthday-section">
                <div class="birthday-cards">
                    <div class="birthday-card boy">
                        <div class="birthday-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <h3><span class="name-yu">Yu</span><span class="birthday-text">的生日</span></h3>
                        <div class="birthday-date">01月16日</div>
                        <div class="birthday-countdown">
                            <span>距离下次生日还有</span>
                            <span class="countdown-days" id="boy-countdown">0</span>
                            <span>天</span>
                        </div>
                    </div>

                    <div class="birthday-card girl">
                        <div class="birthday-icon">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <h3><span class="name-wang">Wang</span><span class="birthday-text">的生日</span></h3>
                        <div class="birthday-date">04月15日</div>
                        <div class="birthday-countdown">
                            <span>距离下次生日还有</span>
                            <span class="countdown-days" id="girl-countdown">0</span>
                            <span>天</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Love quotes section -->
            <section class="love-quotes">
                <div class="quote-card">
                    <button class="quote-btn quote-btn-top-right" onclick="changeQuote()">
                        <i class="fas fa-sync-alt"></i>
                        换一句
                    </button>
                    <i class="fas fa-quote-left quote-icon"></i>
                    <p class="quote-text" id="quote-text">爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。</p>
                </div>
            </section>

            <!-- Love Messages section -->
            <section class="love-messages-section">
                <h2>我们的爱情记录</h2>

                <!-- Message input form -->
                <div class="message-input-card">
                    <div class="message-form">
                        <div class="form-header">
                            <i class="fas fa-pen-fancy"></i>
                            <h3>写下爱的话语</h3>
                        </div>

                        <div class="author-and-template-row">
                            <div class="message-templates">
                                <button class="template-btn" onclick="showTemplates()">
                                    <i class="fas fa-magic"></i>
                                    选择浪漫模板
                                </button>
                            </div>
                            <div class="author-selection">
                                <label class="author-option">
                                    <input type="radio" name="author" value="Yu" checked>
                                    <span class="author-label yu">
                                        <i class="fas fa-heart"></i>
                                        Yu
                                    </span>
                                </label>
                                <label class="author-option">
                                    <input type="radio" name="author" value="Wang">
                                    <span class="author-label wang">
                                        <i class="fas fa-heart"></i>
                                        Wang
                                    </span>
                                </label>
                                <label class="author-option">
                                    <input type="radio" name="author" value="Other">
                                    <span class="author-label other">
                                        <i class="fas fa-heart"></i>
                                        Other
                                    </span>
                                </label>
                            </div>
                        </div>

                        <textarea id="messageText" placeholder="选择一个用户，然后写下你想说的话..."></textarea>

                        <div class="form-actions">
                            <button class="save-btn" onclick="saveMessage()">
                                <i class="fas fa-heart"></i>
                                保存爱的留言
                            </button>
                            <button class="clear-btn" onclick="clearMessage()">
                                <i class="fas fa-eraser"></i>
                                清空
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Message templates modal -->
                <div id="templatesModal" class="templates-modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>选择浪漫模板</h3>
                            <button class="close-btn" onclick="closeTemplates()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="templates-grid" id="templatesGrid">
                            <!-- Templates will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Messages display -->
                <div class="messages-container">
                    <div class="messages-header">
                        <h3>留言板</h3>
                        <div class="messages-actions">
                            <button class="collapse-btn" onclick="toggleMessagesCollapse()" title="折叠">
                                <i class="fas fa-chevron-down" id="collapseIcon"></i>
                            </button>
                            <button class="action-btn" onclick="exportMessages()">
                                <i class="fas fa-download"></i>
                                导出
                            </button>
                            <button class="action-btn" onclick="clearAllMessages()">
                                <i class="fas fa-trash"></i>
                                清空所有
                            </button>
                        </div>
                    </div>
                    <div class="messages-collapsible-content" id="messagesCollapsibleContent">
                        <div id="messagesList" class="messages-list">
                            <!-- Messages will be displayed here -->
                        </div>
                        <div id="emptyState" class="empty-state">
                            <i class="fas fa-heart-broken"></i>
                            <p>还没有留言，快来写下第一条爱的话语吧！</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Memory gallery -->
            <section class="memory-section">
                <h2>我们的美好回忆</h2>
                <div class="memory-grid">
                    <div class="memory-item" onclick="window.location.href = getPageUrl('TOGETHER_DAYS')">
                        <div class="memory-placeholder">
                            <i class="fas fa-calendar-alt"></i>
                            <p>在一起的日子</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href = getPageUrl('ANNIVERSARY')">
                        <div class="memory-placeholder">
                            <i class="fas fa-star"></i>
                            <p>纪念日</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href = getPageUrl('MEMORIAL')">
                        <div class="memory-placeholder">
                            <i class="fas fa-gift"></i>
                            <p>纪念物</p>
                        </div>
                    </div>
                    <div class="memory-item" onclick="window.location.href = getPageUrl('MEETINGS')">
                        <div class="memory-placeholder">
                            <i class="fas fa-users"></i>
                            <p>每一次相遇</p>
                        </div>
                    </div>
                </div>
            </section>


        </main>

        <footer class="footer">
            <p>Made with <i class="fas fa-heart"></i> for our love story</p>
        </footer>
    </div>

    <!-- 引入前端配置 -->
    <script src="/src/client/scripts/config.js"></script>

    <script src="/src/client/romantic-quotes.js?v=2.0"></script>
    <script src="modern-quotes-data.js?v=1.0"></script>
    <script src="/src/client/scripts/script.js?v=modern-quotes-001"></script>
    <script>
        // 浪漫星星功能
        let currentQuoteCategory = 'confession';

        // 初始化浪漫星星
        function initRomanticStars() {
            const stars = document.querySelectorAll('.romantic-star');
            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const category = this.getAttribute('data-quote-category');
                    currentQuoteCategory = category;
                    showRomanticQuote(category);
                });

                // 为每个星星添加随机位置变化功能
                initStarRandomPosition(star);
            });
        }

        // 为星星添加随机位置变化
        function initStarRandomPosition(star) {
            // 获取星星的动画持续时间
            const computedStyle = window.getComputedStyle(star);
            const animationDurations = computedStyle.animationDuration.split(',');
            const twinkleDuration = parseFloat(animationDurations[0]) * 1000; // 第一个动画（闪烁）的持续时间

            // 立即开始第一次位置变化循环
            schedulePositionChange(star, twinkleDuration);
        }

        // 安排位置变化
        function schedulePositionChange(star, duration) {
            // 在动画50%时（完全不透明时）改变位置
            setTimeout(() => {
                changeStarPosition(star);
            }, duration * 0.5);

            // 安排下一次位置变化
            setTimeout(() => {
                schedulePositionChange(star, duration);
            }, duration);
        }

        // 改变星星位置
        function changeStarPosition(star) {
            // 生成安全的随机位置（避免星星出现在边缘被裁剪）
            const minTop = 8;
            const maxTop = 85;
            const minLeft = 8;
            const maxLeft = 85;

            const randomTop = Math.random() * (maxTop - minTop) + minTop;
            const randomLeft = Math.random() * (maxLeft - minLeft) + minLeft;

            // 瞬间改变位置，不使用过渡效果（在完全透明时进行）
            star.style.transition = 'none';
            star.style.top = randomTop + '%';
            star.style.left = randomLeft + '%';

            // 恢复其他动画的过渡效果
            setTimeout(() => {
                star.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            }, 50);
        }

        // 显示浪漫话语
        function showRomanticQuote(category) {
            const quote = getRandomQuoteByCategory(category);
            updateQuoteDisplay(quote);
            document.getElementById('romanticModal').style.display = 'flex';
        }

        // 获取新的话语
        function getNewQuote() {
            const quote = getRandomQuoteByCategory(currentQuoteCategory);
            updateQuoteDisplay(quote);
        }

        // 更新诗词显示
        function updateQuoteDisplay(quote) {
            const quoteTextElement = document.getElementById('romanticQuoteText');
            const quoteAuthorElement = document.getElementById('romanticQuoteAuthor');
            const quoteWorkElement = document.getElementById('romanticQuoteWork');

            // 添加淡出效果
            quoteTextElement.style.opacity = '0';
            quoteAuthorElement.style.opacity = '0';
            quoteWorkElement.style.opacity = '0';

            setTimeout(() => {
                if (typeof quote === 'object' && quote.text) {
                    // 处理诗词换行：在"。"后添加换行
                    const formattedText = quote.text.replace(/。/g, '。<br>');
                    quoteTextElement.innerHTML = formattedText;
                    quoteAuthorElement.textContent = '—— ' + quote.author;
                    quoteWorkElement.textContent = quote.work;
                } else {
                    // 兼容旧格式
                    const formattedText = quote.replace(/。/g, '。<br>');
                    quoteTextElement.innerHTML = formattedText;
                    quoteAuthorElement.textContent = '—— 佚名';
                    quoteWorkElement.textContent = '古典诗词';
                }

                // 强制设置颜色
                quoteTextElement.style.color = '#ec4899';
                quoteTextElement.style.fontWeight = '600';

                // 添加淡入动画
                quoteTextElement.classList.remove('quote-fade-in');
                quoteAuthorElement.classList.remove('quote-fade-in');
                quoteWorkElement.classList.remove('quote-fade-in');

                // 强制重排，然后添加动画类
                void quoteTextElement.offsetWidth;

                quoteTextElement.classList.add('quote-fade-in');
                quoteAuthorElement.classList.add('quote-fade-in');
                quoteWorkElement.classList.add('quote-fade-in');

                // 恢复透明度
                quoteTextElement.style.opacity = '1';
                quoteAuthorElement.style.opacity = '1';
                quoteWorkElement.style.opacity = '1';
            }, 200);
        }

        // 关闭浪漫话语模态框
        function closeRomanticModal() {
            document.getElementById('romanticModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('romanticModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRomanticModal();
            }
        });

        // 折叠/展开留言板功能
        let messagesCollapsed = false;

        function toggleMessagesCollapse() {
            const messagesCollapsibleContent = document.getElementById('messagesCollapsibleContent');
            const collapseIcon = document.getElementById('collapseIcon');

            messagesCollapsed = !messagesCollapsed;

            if (messagesCollapsed) {
                messagesCollapsibleContent.style.display = 'none';
                collapseIcon.className = 'fas fa-chevron-right';
            } else {
                messagesCollapsibleContent.style.display = 'block';
                collapseIcon.className = 'fas fa-chevron-down';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initRomanticStars();

            // 确保留言板内容默认显示
            const messagesCollapsibleContent = document.getElementById('messagesCollapsibleContent');
            if (messagesCollapsibleContent) {
                messagesCollapsibleContent.style.display = 'block';
            }
        });

        // 花朵视频背景加载处理
        const video = document.querySelector('.video-background video');
        const videoContainer = document.querySelector('.video-background');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingProgress = document.getElementById('loadingProgress');

        let progressInterval;
        let currentProgress = 0;

        // 视频加载状态标记
        let videoInitialized = false;
        let videoLoadingStarted = false;

        // 模拟加载进度
        function updateProgress() {
            if (currentProgress < 90) {
                currentProgress += Math.random() * 15;
                if (currentProgress > 90) currentProgress = 90;
                loadingProgress.style.width = currentProgress + '%';
            }
        }

        // 隐藏加载遮罩
        function hideLoadingOverlay() {
            currentProgress = 100;
            loadingProgress.style.width = '100%';
            setTimeout(function() {
                loadingOverlay.classList.add('hidden');
            }, 300);
        }

        if (video) {
            // 开始进度模拟
            progressInterval = setInterval(updateProgress, 200);

            // 视频开始加载
            video.addEventListener('loadstart', function() {
                if (!videoLoadingStarted) {
                    console.log('首页花朵视频开始加载...');
                    videoLoadingStarted = true;
                }
            });

            // 视频加载进度
            video.addEventListener('progress', function() {
                if (!videoInitialized && video.buffered.length > 0) {
                    const buffered = video.buffered.end(0);
                    const duration = video.duration;
                    if (duration > 0) {
                        const realProgress = (buffered / duration) * 90;
                        if (realProgress > currentProgress) {
                            currentProgress = realProgress;
                            loadingProgress.style.width = currentProgress + '%';
                        }
                    }
                }
            });

            // 视频有足够数据可以播放
            video.addEventListener('canplay', function() {
                if (!videoInitialized) {
                    console.log('首页花朵视频可以播放');
                    clearInterval(progressInterval);
                    currentProgress = 95;
                    loadingProgress.style.width = currentProgress + '%';

                    // 确保视频自动播放
                    video.play().catch(function(error) {
                        console.log('视频自动播放被阻止:', error);
                        // 即使播放失败也隐藏加载遮罩
                        hideLoadingOverlay();
                    });
                }
            });

            // 视频加载完成并开始播放
            video.addEventListener('playing', function() {
                if (!videoInitialized) {
                    console.log('首页花朵视频背景加载成功并开始播放');
                    clearInterval(progressInterval);
                    video.classList.add('loaded');
                    videoContainer.classList.add('video-loaded');
                    hideLoadingOverlay();
                    videoInitialized = true; // 标记视频已初始化
                }
            });

            // 视频加载失败
            video.addEventListener('error', function() {
                if (!videoInitialized) {
                    console.log('首页花朵视频背景加载失败，使用花朵主题备用背景');
                    clearInterval(progressInterval);
                    video.style.display = 'none';
                    videoContainer.style.background = 'linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%)';
                    videoContainer.classList.add('video-loaded');
                    hideLoadingOverlay();
                    videoInitialized = true;
                }
            });

            // 设置超时，如果视频加载时间过长，显示花朵主题背景
            setTimeout(function() {
                if (!video.classList.contains('loaded')) {
                    console.log('首页视频加载超时，切换到花朵主题背景');
                    clearInterval(progressInterval);
                    videoContainer.style.background = 'linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%)';
                    videoContainer.classList.add('video-loaded');
                    hideLoadingOverlay();
                }
            }, 10000); // 10秒超时
        } else {
            // 如果没有视频元素，直接隐藏加载遮罩
            hideLoadingOverlay();
        }

        // 页面卸载时清理视频资源
        window.addEventListener('beforeunload', function() {
            const video = document.querySelector('.video-background video');
            if (video) {
                video.pause();
                video.src = '';
                video.load();
                console.log('🧹 页面卸载，已清理视频资源');
            }
        });

        // 页面隐藏时暂停视频，显示时恢复播放
        document.addEventListener('visibilitychange', function() {
            const video = document.querySelector('.video-background video');
            if (video && videoInitialized) {
                if (document.hidden) {
                    video.pause();
                    console.log('📱 页面隐藏，暂停视频播放');
                } else {
                    video.play().catch(function(error) {
                        console.log('📱 页面显示，恢复视频播放失败:', error);
                    });
                    console.log('📱 页面显示，恢复视频播放');
                }
            }
        });
    </script>
</body>
</html>
