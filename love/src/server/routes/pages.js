/**
 * 页面路由
 * 处理静态页面和资源的路由
 */

const express = require('express');
const path = require('path');
const router = express.Router();

// 静态文件服务配置
const staticOptions = {
    maxAge: '1d', // 缓存1天
    etag: true,
    lastModified: true
};

// 主页路由
router.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/index.html'));
});

// 在一起的日子页面
router.get('/together-days', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/together-days.html'));
});

// 纪念日页面
router.get('/anniversary', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/anniversary.html'));
});

// 相遇记录页面
router.get('/meetings', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/meetings.html'));
});

// 纪念页面
router.get('/memorial', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/pages/memorial.html'));
});

// 测试验证页面
router.get('/verify', (req, res) => {
    res.sendFile(path.join(__dirname, '../../../test/test-api.html'));
});

// 新的静态资源路由
router.use('/src/client', express.static(path.join(__dirname, '../../client'), staticOptions));

// 兼容性路由 - 保持旧路径可访问
router.use('/html', express.static(path.join(__dirname, '../../../html'), staticOptions));
router.use('/test', express.static(path.join(__dirname, '../../../test'), staticOptions));
router.use('/background', express.static(path.join(__dirname, '../../../background'), staticOptions));
router.use('/fonts', express.static(path.join(__dirname, '../../../fonts'), staticOptions));

// 主样式文件路由
router.get('/main.css', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/styles/main.css'));
});

// 健康检查页面
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Love site is running',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

module.exports = router;
